<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
    <!-- <PageLayout navTitle="webview" :showFab="false"> -->
    <web-view :src="webviewurl" />
    <!-- </PageLayout> -->
</template>

<script setup lang="ts">
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'

const webviewurl = ref('')

onLoad((opts) => {
    console.log(opts);
    if (opts.permission == 'oneHelp') {
        webviewurl.value = 'https://ai.gxty.com:3270/chat/share?shared_id=7eddda88466711f09563ce7bd080f6f0&from=agent&auth=I4ZTE4MzM4NDY5NDExZjA5ZmYxY2U3Ym&visible_avatar=1&locale=zh'
    } else if (opts.permission == 'twoHelp') {
        webviewurl.value = 'https://ai.gxty.com:3270/chat/share?shared_id=5968d71047ef11f0acc2f6e0c3ddd978&from=agent&auth=I4ZTE4MzM4NDY5NDExZjA5ZmYxY2U3Ym&visible_avatar=1&locale=zh'
    } else if (opts.permission == 'threeHelp') {
        webviewurl.value = 'https://ai.gxty.com:3270/chat/share?shared_id=73d216ac47ef11f09214f6e0c3ddd978&from=agent&auth=I4ZTE4MzM4NDY5NDExZjA5ZmYxY2U3Ym&visible_avatar=1&locale=zh'
    }
})
</script>