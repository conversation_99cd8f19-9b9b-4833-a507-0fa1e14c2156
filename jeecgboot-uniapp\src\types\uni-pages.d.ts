/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/service/service" |
       "/pages/message/message" |
       "/pages/index/index" |
       "/pages/login/login" |
       "/pages/user/people" |
       "/pages-service/marketAction/marketActionActionMap" |
       "/pages-service/marketAction/marketActionAddRecord" |
       "/pages-service/marketAction/marketActionAddRecordMap" |
       "/pages-service/marketAction/marketActionCheckSingleRecord" |
       "/pages-service/marketAction/marketActionChooseExistedClient" |
       "/pages-service/marketAction/marketActionChooseGroupFindAll" |
       "/pages-service/marketAction/marketActionChooseLocation" |
       "/pages-service/marketAction/marketActionChoosePartners" |
       "/pages-service/marketAction/marketActionChoosePeopleFindAll" |
       "/pages-service/marketAction/marketActionLiveSubsidy" |
       "/pages-service/marketAction/marketActionLiveSubsidyAdd" |
       "/pages-service/marketAction/marketActionPartnerAndShare" |
       "/pages-service/marketAction/marketActionReimburse" |
       "/pages-service/marketAction/marketActionReimburseList" |
       "/pages-service/marketAction/marketActionRestRegister" |
       "/pages-service/marketAction/marketActionRestRegisterAdd" |
       "/pages-service/marketAction/marketActionRestRegisterValid" |
       "/pages-service/marketAction/marketActionSelfQuery" |
       "/pages-service/marketAction/marketActionValidPeopleList" |
       "/pages-service/marketAction/marketActionValidRecordList" |
       "/pages-service/marketAction/marketActionVisitClientList" |
       "/pages-service/mission/missionAssign" |
       "/pages-service/mission/missionAssignAdd" |
       "/pages-service/mission/missionGroupAuth" |
       "/pages-service/mission/missionGroupAuthAdd" |
       "/pages-service/webView/webView" |
       "/pages-message/chat/chat" |
       "/pages-message/contacts/contacts" |
       "/pages-message/personPage/personPage" |
       "/pages-message/tenant/tenant" |
       "/pages-user/location/location" |
       "/pages-user/userEdit/userEdit" |
       "/pages-work/dragPage/index" |
       "/pages-work/onlinePage/onlineAdd" |
       "/pages-work/onlinePage/onlineDetail" |
       "/pages-work/onlinePage/onlineEdit" |
       "/pages-sub/online/online" |
       "/pages-sub/online/onlineCard" |
       "/pages-sub/online/onlineTable" |
       "/components/TreeSelect/TreeSelect" |
       "/components/SelectUser/SelectUser" |
       "/components/SelectDept/SelectDept" |
       "/components/Popup/Popup" |
       "/components/PopupDict/PopupDict" |
       "/components/online/online-loader" |
       "/components/online/view/link-records-modal" |
       "/components/online/view/online-checkbox" |
       "/components/online/view/online-date" |
       "/components/online/view/online-datetime" |
       "/components/online/view/online-image" |
       "/components/online/view/online-multi" |
       "/components/online/view/online-pca" |
       "/components/online/view/online-popup-link-record" |
       "/components/online/view/online-radio" |
       "/components/online/view/online-select" |
       "/components/online/view/online-time" |
       "/components/CategorySelect/CategorySelect" |
       "/components/ProgressMap/ProgressMap" |
       "/components/ImgPreview/ImgPreview" |
       "/components/BottomOperate/BottomOperate" |
       "/components/RightConditionFilter/RightConditionFilter";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/user/people"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
